<script setup lang="ts">
import { ref } from 'vue'
import type { ProductLinkDrawerInstance } from '@src-console/views/ProductList/templates'
import { CardCommon } from '@src-console/components'

const visible = ref(false);

function show() {
  visible.value = true;
}
defineExpose<ProductLinkDrawerInstance>({
  show,
});
function handleLinkProduct() {
  console.log('handleLinkProduct');
}
</script>

<template>
  <t-drawer v-model:visible="visible"
            size="450px"
            :footer="false"
            destroy-on-close>
    <CardCommon border-color="#bfbfbf"
                @click="handleLinkProduct">
      <div class="flex gap-4">
        <!--  产品 logo  -->
        <div class="w-12 product-logo">
          <img class="w-full h-auto"
               src="https://vip.helloimg.com/i/2024/05/17/6646a58130f97.png"
               alt="logo">
        </div>
        <!--  产品信息  -->
        <div>
          <div class="font-bold">元组件桌面</div>
          <div class="text-size-xs text-gray-500">打造一个由元组件组成的定制化桌面</div>
        </div>
      </div>
    </CardCommon>
  </t-drawer>
</template>

<style lang="less" scoped>
</style>
