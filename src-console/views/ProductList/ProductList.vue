<script setup lang="ts">
import { CardCommon, CardGroup } from '@src-console/components'
import { ProductLinkDrawer } from './templates'
import type { ProductLinkDrawerInstance } from './templates'
import { useTemplateRef } from 'vue'
import { isDesktop } from '@src-common/utils'

const productLinkDrawerRef = useTemplateRef<ProductLinkDrawerInstance>('productLinkDrawerRef')
</script>

<template>
  <ProductLinkDrawer v-if="isDesktop()" ref="productLinkDrawerRef" />
  <div>
    <CardGroup title="产品列表"
               border>
      <template #extend>
        <div class="ut:block hidden">
          <t-button @click="productLinkDrawerRef?.show()">
            <template #icon>
              <t-icon class="i-u-linkPlus"></t-icon>
            </template>
            绑定产品
          </t-button>
        </div>
      </template>
      <CardCommon>
        <div class="flex justify-between mt-2">
          <div class="flex gap-4">
            <!--  产品 logo  -->
            <div class="w-12 product-logo">
              <img class="w-full h-auto"
                   src="https://vip.helloimg.com/i/2024/05/17/6646a58130f97.png"
                   alt="logo">
            </div>
            <!--  产品信息  -->
            <div>
              <div class="font-bold">元组件桌面</div>
              <div class="text-size-xs text-gray-500">打造一个由元组件组成的定制化桌面</div>
            </div>
          </div>
          <div>
            <t-button variant="outline">详情</t-button>
          </div>
        </div>
      </CardCommon>
    </CardGroup>
  </div>
</template>

<style scoped>

</style>
