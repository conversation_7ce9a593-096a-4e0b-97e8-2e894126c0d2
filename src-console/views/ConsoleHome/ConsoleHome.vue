<script setup lang="ts">
// 重定向到主布局
import { Layout as TLayout } from 'tdesign-vue-next'
import { LeftMenu } from '@src-console/components'
import { getUserInfo } from '@src-common/api/userApi.ts'

function test() {
  getUserInfo();
}
</script>
<template>
  <t-button @click="test">111</t-button>
  <t-layout class="h-screen">
    <t-header>
      <t-head-menu value="item1" height="60px">
        <template #logo>
          <div style="width: 160px;">
            我是一个 logo
          </div>
        </template>
        <template #operations>
          <a href="javascript:;"><t-icon class="t-menu__operations-icon" name="home" /></a>
        </template>
      </t-head-menu>
    </t-header>
    <t-layout class="min-h-0">
      <t-aside width="auto">
        <LeftMenu />
      </t-aside>
      <t-layout style="background: #F2F4F8;" class="min-h-0">
        <t-content class="xl:p-4 ut:p-2 p-1 h-full overflow-hidden">
          <div class="main-content">
            <router-view />
          </div>
        </t-content>
      </t-layout>
    </t-layout>
  </t-layout>
</template>
<style lang="less" scoped>
.main-content {
  --at-apply: h-full rounded-lg;
  background-color: var(--u-bg-color);
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
