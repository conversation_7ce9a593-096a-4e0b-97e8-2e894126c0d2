import { createFetch } from 'ofetch';
import type { FetchOptions } from 'ofetch';

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

export enum RequestMethod {
  GET = 'GET',
  POST = 'POST',
}

export interface IResponse<T> {
  code: number;
  data: T;
  msg: string;
}

export const request = createFetch({
  defaults: {
    baseURL: BASE_URL,
    timeout: 5000,
    onResponse: async({ request, response, options }) => {
      console.log("[fetch response]", request, response.status, response.body, options);
    }
  }
})
